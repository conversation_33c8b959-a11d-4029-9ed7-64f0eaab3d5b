const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n');

// Parse environment variables - handle multiline JSON
const env = {};
let currentKey = null;
let currentValue = "";
let inMultilineValue = false;

envLines.forEach((line) => {
  if (line.trim() && !line.startsWith('#')) {
    if (inMultilineValue) {
      if (line.trim().endsWith("'")) {
        // End of multiline value
        currentValue += line.trim().slice(0, -1); // Remove trailing quote
        env[currentKey] = currentValue;
        currentKey = null;
        currentValue = "";
        inMultilineValue = false;
      } else {
        currentValue += line;
      }
    } else {
      const equalIndex = line.indexOf('=');
      if (equalIndex > 0) {
        const key = line.substring(0, equalIndex).trim();
        const value = line.substring(equalIndex + 1).trim();
        
        if (value.startsWith("'{") && !value.endsWith("}'")) {
          // Start of multiline JSON value
          currentKey = key;
          currentValue = value.substring(1); // Remove leading quote
          inMultilineValue = true;
        } else if (value.startsWith("'") && value.endsWith("'")) {
          // Single line quoted value
          env[key] = value.slice(1, -1); // Remove quotes
        } else {
          env[key] = value;
        }
      }
    }
  }
});

// Initialize Firebase Admin
const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT_KEY);

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id,
  });
}

const db = admin.firestore();

async function fixBookingUserId() {
  try {
    console.log('Fixing booking user ID...');
    
    const bookingId = '5A0aTsU2FYaKweUUyFho';
    const experienceId = '3GPg2GHQFOJK8sATgMXI';
    const oldUserId = '6oqh095Hj0YcWH1dy8sNhVHouBr1';
    const newUserId = 'U8TeGBYI2WV4DuBgXF1Ep24eZQF2';
    
    console.log(`\n1. Getting booking from experience collection...`);
    // Get the booking from the experience collection
    const experienceBookingRef = db.collection('localExperiences').doc(experienceId).collection('bookings').doc(bookingId);
    const experienceBookingDoc = await experienceBookingRef.get();
    
    if (!experienceBookingDoc.exists) {
      console.log('❌ Booking not found in experience collection');
      return;
    }
    
    const bookingData = experienceBookingDoc.data();
    console.log('✅ Found booking in experience collection');
    console.log('Current userId:', bookingData.userId);
    
    console.log(`\n2. Updating booking with correct user ID...`);
    // Update the booking with the correct user ID
    const updatedBookingData = {
      ...bookingData,
      userId: newUserId,
    };
    
    // Update in experience collection
    await experienceBookingRef.update({ userId: newUserId });
    console.log('✅ Updated booking in experience collection');
    
    console.log(`\n3. Creating booking in correct user collection...`);
    // Create the booking in the correct user's collection
    const newUserBookingRef = db.collection('users').doc(newUserId).collection('localExperienceBookings').doc(bookingId);
    await newUserBookingRef.set(updatedBookingData);
    console.log('✅ Created booking in correct user collection');
    
    console.log(`\n4. Removing booking from old user collection (if exists)...`);
    // Remove from old user collection if it exists
    const oldUserBookingRef = db.collection('users').doc(oldUserId).collection('localExperienceBookings').doc(bookingId);
    const oldUserBookingDoc = await oldUserBookingRef.get();
    
    if (oldUserBookingDoc.exists) {
      await oldUserBookingRef.delete();
      console.log('✅ Removed booking from old user collection');
    } else {
      console.log('ℹ️ Booking was not in old user collection');
    }
    
    console.log(`\n5. Verifying the fix...`);
    // Verify the booking exists in the correct user collection
    const verifyBookingRef = db.collection('users').doc(newUserId).collection('localExperienceBookings').doc(bookingId);
    const verifyBookingDoc = await verifyBookingRef.get();
    
    if (verifyBookingDoc.exists) {
      const verifyData = verifyBookingDoc.data();
      console.log('✅ Verification successful');
      console.log('Booking details:', {
        id: verifyBookingDoc.id,
        userId: verifyData.userId,
        userEmail: verifyData.userEmail,
        status: verifyData.status,
      });
    } else {
      console.log('❌ Verification failed - booking not found in correct user collection');
    }
    
  } catch (error) {
    console.error('❌ Error fixing booking user ID:', error);
    throw error;
  }
}

// Run the script
fixBookingUserId()
  .then(() => {
    console.log('\n🎉 Booking user ID fix completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
