const admin = require('firebase-admin');

// Initialize Firebase Admin
const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id,
  });
}

const db = admin.firestore();

async function createDummyBooking() {
  try {
    console.log('Creating dummy <NAME_EMAIL>...');
    
    // User details
    const userId = '6oqh095Hj0YcWH1dy8sNhVHouBr1';
    const userEmail = '<EMAIL>';
    const userName = 'DUMMY ACCOUNT00';
    
    // Experience details (using the Street Art tour)
    const experienceId = '3GPg2GHQFOJK8sATgMXI';
    const experienceTitle = 'Street Art & Murals Walking Tour';
    const experienceLocation = 'San Francisco, United States';
    const experienceHost = '<PERSON>';
    
    // Booking details - set date to yesterday so it can be completed
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const bookingDate = yesterday.toISOString().split('T')[0]; // YYYY-MM-DD format
    const bookingTime = '14:00';
    const availabilityId = 'slot-14-00';
    
    // Create booking data
    const bookingData = {
      // Experience details
      experienceId,
      experienceTitle,
      experienceLocation,
      experienceHost,
      
      // Booking details
      userId,
      userEmail,
      userName,
      date: bookingDate,
      time: bookingTime,
      availabilityId,
      guests: 2,
      guestDetails: [
        {
          name: userName,
          email: userEmail,
        }
      ],
      
      // Pricing
      pricing: {
        basePrice: 45,
        guests: 2,
        subtotal: 90,
        taxes: 0,
        fees: 0,
        total: 90,
        currency: 'USD',
      },
      
      // Status - set as confirmed and paid so it can be completed
      status: 'confirmed',
      paymentStatus: 'paid',
      
      // Timestamps
      bookedAt: admin.firestore.Timestamp.now(),
      confirmedAt: admin.firestore.Timestamp.now(),
      
      // Mock Stripe data
      stripeSessionId: 'cs_test_dummy_session_123',
      stripePaymentIntentId: 'pi_test_dummy_payment_123',
    };
    
    // Create booking ID
    const bookingRef = db.collection('localExperiences').doc(experienceId).collection('bookings').doc();
    const bookingId = bookingRef.id;
    
    // Add ID to booking data
    const finalBookingData = {
      ...bookingData,
      id: bookingId,
    };
    
    // Create booking in experience collection
    await bookingRef.set(finalBookingData);
    console.log(`✅ Created booking in experience collection: ${bookingId}`);
    
    // Create booking in user collection
    const userBookingRef = db.collection('users').doc(userId).collection('localExperienceBookings').doc(bookingId);
    await userBookingRef.set(finalBookingData);
    console.log(`✅ Created booking in user collection: ${bookingId}`);
    
    console.log('🎉 Dummy booking created successfully!');
    console.log(`Booking ID: ${bookingId}`);
    console.log(`Experience: ${experienceTitle}`);
    console.log(`Date: ${bookingDate} at ${bookingTime}`);
    console.log(`User: ${userName} (${userEmail})`);
    console.log(`Status: ${bookingData.status} / ${bookingData.paymentStatus}`);
    
    return bookingId;
    
  } catch (error) {
    console.error('❌ Error creating dummy booking:', error);
    throw error;
  }
}

// Run the script
createDummyBooking()
  .then((bookingId) => {
    console.log(`\n🚀 Next step: Run the completion cron job to mark this booking as completed`);
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
