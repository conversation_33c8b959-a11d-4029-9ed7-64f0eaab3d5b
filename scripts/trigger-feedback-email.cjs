const admin = require("firebase-admin")
const fs = require("fs")
const path = require("path")

// Load environment variables from .env.local
const envPath = path.join(__dirname, "..", ".env.local")
const envContent = fs.readFileSync(envPath, "utf8")
const envLines = envContent.split("\n")

// Parse environment variables - handle multiline JSON
const env = {}
let currentKey = null
let currentValue = ""
let inMultilineValue = false

envLines.forEach((line) => {
  if (line.trim() && !line.startsWith("#")) {
    if (inMultilineValue) {
      if (line.trim().endsWith("'")) {
        // End of multiline value
        currentValue += line.trim().slice(0, -1) // Remove trailing quote
        env[currentKey] = currentValue
        currentKey = null
        currentValue = ""
        inMultilineValue = false
      } else {
        currentValue += line
      }
    } else {
      const equalIndex = line.indexOf("=")
      if (equalIndex > 0) {
        const key = line.substring(0, equalIndex).trim()
        const value = line.substring(equalIndex + 1).trim()

        if (value.startsWith("'{") && !value.endsWith("}'")) {
          // Start of multiline JSON value
          currentKey = key
          currentValue = value.substring(1) // Remove leading quote
          inMultilineValue = true
        } else if (value.startsWith("'") && value.endsWith("'")) {
          // Single line quoted value
          env[key] = value.slice(1, -1) // Remove quotes
        } else {
          env[key] = value
        }
      }
    }
  }
})

// Initialize Firebase Admin
const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT_KEY)

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id,
  })
}

const db = admin.firestore()

async function triggerFeedbackEmail() {
  try {
    console.log("Triggering feedback email for booking 5A0aTsU2FYaKweUUyFho...")

    const experienceId = "3GPg2GHQFOJK8sATgMXI"
    const bookingId = "5A0aTsU2FYaKweUUyFho"

    // Get the booking document
    const bookingRef = db
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)
    const bookingDoc = await bookingRef.get()

    if (!bookingDoc.exists) {
      console.error("Booking not found!")
      return
    }

    const bookingData = bookingDoc.data()
    console.log("Current booking status:", bookingData.status)
    console.log("Current payment status:", bookingData.paymentStatus)

    // Update the booking to trigger the Firebase function
    // We'll just update the updatedAt timestamp to trigger the function
    await bookingRef.update({
      updatedAt: admin.firestore.Timestamp.now(),
      // Add a trigger field to ensure the function processes this as a completion
      triggerFeedback: true,
    })

    console.log("✅ Booking updated to trigger Firebase function")
    console.log("🔥 Firebase function should now send feedback <NAME_EMAIL>")
    console.log("📧 Check your email for the feedback link!")
  } catch (error) {
    console.error("❌ Error triggering feedback email:", error)
    throw error
  }
}

// Run the script
triggerFeedbackEmail()
  .then(() => {
    console.log("\n🎉 Feedback email trigger completed!")
    process.exit(0)
  })
  .catch((error) => {
    console.error("Script failed:", error)
    process.exit(1)
  })
