const admin = require("firebase-admin")
const fs = require("fs")
const path = require("path")

// Load environment variables from .env.local
const envPath = path.join(__dirname, "..", ".env.local")
const envContent = fs.readFileSync(envPath, "utf8")
const envLines = envContent.split("\n")

// Parse environment variables - handle multiline JSON
const env = {}
let currentKey = null
let currentValue = ""
let inMultilineValue = false

envLines.forEach((line) => {
  if (line.trim() && !line.startsWith("#")) {
    if (inMultilineValue) {
      if (line.trim().endsWith("'")) {
        // End of multiline value
        currentValue += line.trim().slice(0, -1) // Remove trailing quote
        env[currentKey] = currentValue
        currentKey = null
        currentValue = ""
        inMultilineValue = false
      } else {
        currentValue += line
      }
    } else {
      const equalIndex = line.indexOf("=")
      if (equalIndex > 0) {
        const key = line.substring(0, equalIndex).trim()
        const value = line.substring(equalIndex + 1).trim()

        if (value.startsWith("'{") && !value.endsWith("}'")) {
          // Start of multiline JSON value
          currentKey = key
          currentValue = value.substring(1) // Remove leading quote
          inMultilineValue = true
        } else if (value.startsWith("'") && value.endsWith("'")) {
          // Single line quoted value
          env[key] = value.slice(1, -1) // Remove quotes
        } else {
          env[key] = value
        }
      }
    }
  }
})

// Initialize Firebase Admin
const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT_KEY)

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id,
  })
}

const db = admin.firestore()

async function testBookingService() {
  try {
    console.log("Testing booking service...")

    const bookingId = "5A0aTsU2FYaKweUUyFho"
    const experienceId = "3GPg2GHQFOJK8sATgMXI"

    console.log(`\n1. Testing direct path query...`)
    // Test direct path query
    const directRef = db
      .collection("localExperiences")
      .doc(experienceId)
      .collection("bookings")
      .doc(bookingId)
    const directDoc = await directRef.get()

    if (directDoc.exists) {
      console.log("✅ Direct path query successful")
      console.log("Booking data:", {
        id: directDoc.id,
        status: directDoc.data().status,
        userEmail: directDoc.data().userEmail,
      })
    } else {
      console.log("❌ Direct path query failed - booking not found")
    }

    console.log(`\n2. Testing collection group query...`)
    // Test collection group query (what the service uses) - using 'id' field
    const collectionGroupQuery = db
      .collectionGroup("bookings")
      .where("id", "==", bookingId)
      .limit(1)
    const collectionGroupSnapshot = await collectionGroupQuery.get()

    if (!collectionGroupSnapshot.empty) {
      console.log("✅ Collection group query successful")
      const doc = collectionGroupSnapshot.docs[0]
      console.log("Booking data:", {
        id: doc.id,
        status: doc.data().status,
        userEmail: doc.data().userEmail,
      })
    } else {
      console.log("❌ Collection group query failed - booking not found")
    }

    console.log(`\n3. Testing alternative collection group query...`)
    // Test alternative collection group query using id field
    const altQuery = db.collectionGroup("bookings").where("id", "==", bookingId).limit(1)
    const altSnapshot = await altQuery.get()

    if (!altSnapshot.empty) {
      console.log("✅ Alternative collection group query successful")
      const doc = altSnapshot.docs[0]
      console.log("Booking data:", {
        id: doc.id,
        status: doc.data().status,
        userEmail: doc.data().userEmail,
      })
    } else {
      console.log("❌ Alternative collection group query failed - booking not found")
    }

    console.log(`\n4. Testing user booking collection...`)
    // Test user booking collection
    const userId = "6oqh095Hj0YcWH1dy8sNhVHouBr1"
    const userBookingRef = db
      .collection("users")
      .doc(userId)
      .collection("localExperienceBookings")
      .doc(bookingId)
    const userBookingDoc = await userBookingRef.get()

    if (userBookingDoc.exists) {
      console.log("✅ User booking collection query successful")
      console.log("Booking data:", {
        id: userBookingDoc.id,
        status: userBookingDoc.data().status,
        userEmail: userBookingDoc.data().userEmail,
      })
    } else {
      console.log("❌ User booking collection query failed - booking not found")
    }
  } catch (error) {
    console.error("❌ Error testing booking service:", error)
    throw error
  }
}

// Run the script
testBookingService()
  .then(() => {
    console.log("\n🎉 Booking service test completed!")
    process.exit(0)
  })
  .catch((error) => {
    console.error("Script failed:", error)
    process.exit(1)
  })
